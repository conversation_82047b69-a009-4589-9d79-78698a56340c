<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Confluent.Kafka" Version="9.3.1"/>
    <PackageReference Include="Aspire.MongoDB.Driver.v3" Version="9.3.1"/>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6"/>
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.6"/>
    <PackageReference Include="Polly" Version="8.6.1"/>
    <PackageReference Include="Polly.Extensions" Version="8.6.1"/>
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.0"/>
    <PackageReference Include="Serilog" Version="4.3.0"/>
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0"/>
    <PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1"/>
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1"/>
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0"/>
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AspireApp2.ServiceDefaults\AspireApp2.ServiceDefaults.csproj"/>
  </ItemGroup>

</Project>
