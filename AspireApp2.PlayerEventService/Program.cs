using AspireApp2.PlayerEventService.Configuration;
using AspireApp2.PlayerEventService.HealthChecks;
using AspireApp2.PlayerEventService.Interfaces;
using AspireApp2.PlayerEventService.Repositories;
using AspireApp2.PlayerEventService.Services;
using AspireApp2.ServiceDefaults;
using Confluent.Kafka;
using Microsoft.Extensions.Options;
using MongoDB.Driver;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// 1) Bind & validate your settings early
builder.Services
    .AddOptions<MongoDbSettings>()
    .Bind(builder.Configuration.GetSection(MongoDbSettings.SectionName))
    .ValidateDataAnnotations()
    .ValidateOnStart();

builder.Services
    .AddOptions<KafkaSettings>()
    .Bind(builder.Configuration.GetSection(KafkaSettings.SectionName))
    .ValidateDataAnnotations()
    .ValidateOnStart();

// 2) Aspire's MongoDB client registration
builder.AddMongoDBClient("mongodb");

// 3) Strongly-typed registration of IMongoDatabase
builder.Services.AddSingleton<IMongoDatabase>(sp =>
{
    MongoDbSettings settings = sp.GetRequiredService<IOptions<MongoDbSettings>>().Value;
    IMongoClient client = sp.GetRequiredService<IMongoClient>();
    return client.GetDatabase("PlayerEvents");
});

// 4) Kafka wiring with better resilience
builder.AddKafkaConsumer<string, string>("kafka", settings =>
{
    KafkaSettings cfg = builder.Configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>()!;
    settings.Config.GroupId = cfg.Consumer.GroupId;
    settings.Config.AutoOffsetReset = Enum.Parse<AutoOffsetReset>(cfg.Consumer.AutoOffsetReset, true);
    settings.Config.EnableAutoCommit = cfg.Consumer.EnableAutoCommit;
    settings.Config.SessionTimeoutMs = cfg.Consumer.SessionTimeoutMs;
    settings.Config.MaxPollIntervalMs = cfg.Consumer.MaxPollIntervalMs;
    settings.Config.EnablePartitionEof = false;
    settings.Config.BrokerAddressFamily = BrokerAddressFamily.V4;
    // Add connection resilience
    settings.Config.SocketTimeoutMs = 60_000;
    settings.Config.SocketKeepaliveEnable = true;
    settings.Config.ReconnectBackoffMs = 100;
    settings.Config.ReconnectBackoffMaxMs = 10_000;
    settings.Config.TopicMetadataRefreshIntervalMs = 300_000;
});

builder.AddKafkaProducer<string, string>("kafka", settings =>
{
    settings.Config.Acks = Acks.All;
    settings.Config.EnableIdempotence = true;
    settings.Config.MessageTimeoutMs = 30_000;
    settings.Config.RetryBackoffMs = 100;
    settings.Config.MessageSendMaxRetries = 3;
    settings.Config.CompressionType = CompressionType.Snappy;
    settings.Config.ClientId = $"{Environment.MachineName}-dlq-producer";
    settings.Config.BrokerAddressFamily = BrokerAddressFamily.V4;
    // Add connection resilience
    settings.Config.SocketTimeoutMs = 60_000;
    settings.Config.SocketKeepaliveEnable = true;
    settings.Config.ReconnectBackoffMs = 100;
    settings.Config.ReconnectBackoffMaxMs = 10_000;
});

// 5) Application services & conditional consumer
builder.Services.AddScoped<IPlayerEventRepository, PlayerEventRepository>();
builder.Services.AddSingleton<IResilienceService, ResilienceService>();
builder.Services.AddSingleton<IDeadLetterQueueService, DeadLetterQueueService>();

// 6) Add service defaults for health checks and observability
builder.AddServiceDefaults();

KafkaSettings? kafkaSettings = builder.Configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>();
if (kafkaSettings?.EnableConsumer == true)
{
    builder.Services.AddHostedService<PlayerEventConsumerService>();
}
else
{
    // Use logger from DI container
    builder.Services.AddLogging();
}

// 7) Health checks
builder.Services.AddHealthChecks()
    .AddCheck<MongoDbHealthCheck>("mongodb", tags: ["db", "mongodb"])
    .AddCheck<KafkaHealthCheck>("kafka", tags: ["messaging", "kafka"]);

// 8) MVC, OpenAPI, ProblemDetails
builder.Services.AddControllers();
builder.Services.AddOpenApi();
builder.Services.AddProblemDetails();

// Build & run
WebApplication app = builder.Build();

// Log startup information if consumer is disabled
if (kafkaSettings?.EnableConsumer != true)
{
    ILogger<Program> logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogInformation("Kafka consumer disabled via configuration.");
}

app.UseExceptionHandler("/error");
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.MapOpenApi();
}

app.UseHttpsRedirection();
app.MapGet("/status",
        () => Results.Ok(new { Service = "PlayerEventService", Status = "Running", Timestamp = DateTime.UtcNow }))
    .WithName("GetStatus");
app.Map("/error", () => Results.Problem("An error occurred."));
app.MapControllers();
app.MapDefaultEndpoints();
app.Run();
