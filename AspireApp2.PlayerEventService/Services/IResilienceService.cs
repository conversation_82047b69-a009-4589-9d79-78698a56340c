namespace AspireApp2.PlayerEventService.Services;

public interface IResilienceService : IDisposable
{
    Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default);

    Task ExecuteWithRetryAsync(Func<Task> operation, string operationName,
        CancellationToken cancellationToken = default);

    Task<T> ExecuteWithCircuitBreakerAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default);

    Task ExecuteWithCircuitBreakerAsync(Func<Task> operation, string operationName,
        CancellationToken cancellationToken = default);

    Task<T> ExecuteWithFullResilienceAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default);

    Task<T> ExecuteWithFallbackAsync<T>(Func<Task<T>> operation, Func<Task<T>> fallbackOperation, 
        string operationName, CancellationToken cancellationToken = default);
}
