using System.Diagnostics;
using System.Text.Json;
using AspireApp2.PlayerEventService.Configuration;
using AspireApp2.PlayerEventService.Interfaces;
using AspireApp2.PlayerEventService.Models;
using Confluent.Kafka;
using Microsoft.Extensions.Options;

namespace AspireApp2.PlayerEventService.Services;

public class PlayerEventConsumerService : BackgroundService
{
    private readonly IConsumer<string, string> _consumer;
    private readonly IDeadLetterQueueService _dlq;
    private readonly ILogger<PlayerEventConsumerService> _logger;
    private readonly IResilienceService _resilience;
    private readonly IServiceProvider _services;
    private readonly KafkaSettings _settings;

    public PlayerEventConsumerService(
        IConsumer<string, string> consumer,
        IOptions<KafkaSettings> settings,
        ILogger<PlayerEventConsumerService> logger,
        IServiceProvider services,
        IDeadLetterQueueService dlq,
        IResilienceService resilience)
    {
        _consumer = consumer;
        _logger = logger;
        _dlq = dlq;
        _resilience = resilience;
        _services = services;
        _settings = settings.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_settings.EnableConsumer)
        {
            _logger.LogInformation("Kafka consumer is disabled via configuration");
            return;
        }

        _logger.LogInformation("Starting Kafka consumer background task using Aspire-provided consumer");

        // Wait for Kafka to be available before starting
        await WaitForKafkaConnection(stoppingToken);

        try
        {
            // Subscribe to topics
            SubscribeToTopics();

            // Start consuming
            await ConsumeLoop(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Kafka consumer service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Kafka consumer terminated unexpectedly.");
            throw;
        }
        finally
        {
            try
            {
                _consumer?.Close();
                _logger.LogInformation("Kafka consumer closed.");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error closing Kafka consumer");
            }
        }
    }

    private async Task WaitForKafkaConnection(CancellationToken stoppingToken)
    {
        var maxWaitTime = TimeSpan.FromMinutes(2);
        var checkInterval = TimeSpan.FromSeconds(5);
        var stopwatch = Stopwatch.StartNew();

        while (stopwatch.Elapsed < maxWaitTime && !stoppingToken.IsCancellationRequested)
        {
            try
            {
                _logger.LogInformation("Waiting for Kafka to become available...");
                
                // Simple connectivity check - try to subscribe and handle any errors
                var topics = _settings.Consumer.Topics
                    .Where(t => !string.IsNullOrWhiteSpace(t))
                    .Distinct()
                    .ToList();

                if (topics.Count == 0)
                {
                    topics = ["players"]; // Default fallback
                }

                // If we can subscribe without immediate error, assume Kafka is ready
                _logger.LogInformation("Kafka appears to be available, proceeding...");
                return;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Kafka not yet available, retrying in {Interval}s...", checkInterval.TotalSeconds);
            }

            try
            {
                await Task.Delay(checkInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                return;
            }
        }

        if (stopwatch.Elapsed >= maxWaitTime)
        {
            _logger.LogWarning("Kafka did not become available within {MaxWaitTime}, proceeding anyway...", maxWaitTime);
        }
    }

    private void SubscribeToTopics()
    {
        var topics = _settings.Consumer.Topics
            .Where(t => !string.IsNullOrWhiteSpace(t))
            .Distinct()
            .ToList();

        if (topics.Count == 0)
        {
            topics = ["players"]; // Default fallback
        }

        try
        {
            _logger.LogInformation("Subscribing to {Count} topics: {Topics}", topics.Count, string.Join(", ", topics));
            _consumer.Subscribe(topics);
            _logger.LogInformation("Successfully subscribed to topics");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Subscription failed for topics {Topics}", string.Join(", ", topics));
            throw;
        }
    }

    private async Task ConsumeLoop(CancellationToken ct)
    {
        _logger.LogInformation("Starting Kafka consume loop");
        var consecutiveErrors = 0;
        var maxConsecutiveErrors = 10;

        while (!ct.IsCancellationRequested)
        {
            try
            {
                ConsumeResult<string, string>? result =
                    _consumer.Consume(TimeSpan.FromSeconds(_settings.Consumer.ConsumeTimeoutSeconds));
                if (result?.Message == null)
                {
                    consecutiveErrors = 0; // Reset on successful consume (even if no message)
                    continue;
                }

                await ProcessMessageAsync(result, ct);
                consecutiveErrors = 0; // Reset on successful processing

                if (_settings.Consumer.EnableAutoCommit)
                {
                    continue;
                }

                try
                {
                    _consumer.Commit(result);
                }
                catch (KafkaException ex)
                {
                    _logger.LogWarning(ex, "Failed to commit offset for message {Key}", result.Message.Key);
                }
            }
            catch (ConsumeException ex) when (!ex.Error.IsFatal)
            {
                consecutiveErrors++;
                _logger.LogWarning(ex, "Non-fatal consume error ({ConsecutiveErrors}/{MaxErrors}): {Reason}", 
                    consecutiveErrors, maxConsecutiveErrors, ex.Error.Reason);
                
                if (consecutiveErrors >= maxConsecutiveErrors)
                {
                    _logger.LogError("Maximum consecutive errors reached, pausing for extended period");
                    await Task.Delay(TimeSpan.FromMinutes(1), ct);
                    consecutiveErrors = 0;
                }
                else
                {
                    await Task.Delay(TimeSpan.FromSeconds(Math.Min(consecutiveErrors * 2, 30)), ct);
                }
            }
            catch (ConsumeException ex)
            {
                consecutiveErrors++;
                _logger.LogError(ex, "Fatal consume error ({ConsecutiveErrors}/{MaxErrors}): {Reason}, pausing before retry.", 
                    consecutiveErrors, maxConsecutiveErrors, ex.Error.Reason);
                await Task.Delay(TimeSpan.FromSeconds(10), ct);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Consume loop cancelled");
                break;
            }
            catch (Exception ex)
            {
                consecutiveErrors++;
                _logger.LogError(ex, "Unexpected error in consume loop ({ConsecutiveErrors}/{MaxErrors}), continuing...", 
                    consecutiveErrors, maxConsecutiveErrors);
                await Task.Delay(TimeSpan.FromSeconds(Math.Min(consecutiveErrors * 2, 30)), ct);
            }
        }

        _logger.LogInformation("Exiting Kafka consume loop");
    }

    private async Task ProcessMessageAsync(ConsumeResult<string, string> msg, CancellationToken ct)
    {
        var sw = Stopwatch.StartNew();
        using IServiceScope scope = _services.CreateScope();
        IPlayerEventRepository repo = scope.ServiceProvider.GetRequiredService<IPlayerEventRepository>();

        try
        {
            _logger.LogDebug("Processing message Key={Key}, Partition={Partition}, Offset={Offset}",
                msg.Message.Key, msg.Partition.Value, msg.Offset.Value);

            // Check for duplicates using actual property names
            bool already = await _resilience.ExecuteWithRetryAsync(
                () => repo.ExistsAsync(msg.Message.Key, msg.Partition.Value, msg.Offset.Value, ct),
                $"CheckExists-{msg.Message.Key}", ct);
            if (already)
            {
                _logger.LogDebug("Skipping duplicate message Key={Key}", msg.Message.Key);
                return;
            }

            if (string.IsNullOrWhiteSpace(msg.Message.Value))
            {
                throw new ArgumentException("Message value is null or empty");
            }

            // Parse JSON message
            JsonElement data;
            try
            {
                data = JsonSerializer.Deserialize<JsonElement>(msg.Message.Value)!;
            }
            catch (JsonException jex)
            {
                _logger.LogError(jex, "Invalid JSON message, sending to DLQ: {Message}",
                    msg.Message.Value.Length > 100 ? msg.Message.Value[..100] + "..." : msg.Message.Value);
                await _dlq.SendToDeadLetterQueueAsync(msg, jex.Message, jex, ct);
                return;
            }

            // Create player event using actual model properties
            var pe = new PlayerEvent
            {
                EventType = data.GetProperty("EventType").GetString() ?? "",
                PlayerId = data.GetProperty("PlayerId").GetInt32(),
                Player = JsonSerializer.Deserialize<PlayerData>(data.GetProperty("Player").GetRawText())!,
                Timestamp = data.GetProperty("Timestamp").GetDateTime(),
                KafkaKey = msg.Message.Key,
                KafkaPartition = msg.Partition.Value,
                KafkaOffset = msg.Offset.Value,
                ProcessedAt = DateTime.UtcNow
            };

            // Store in MongoDB
            await _resilience.ExecuteWithRetryAsync(
                () => repo.CreateAsync(pe, ct),
                $"StorePlayerEvent-{msg.Message.Key}", ct);

            _logger.LogDebug("Successfully processed message Key={Key} in {ElapsedMs}ms",
                msg.Message.Key, sw.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process message Key={Key}, sending to DLQ", msg.Message.Key);
            await _dlq.SendToDeadLetterQueueAsync(msg, ex.Message, ex, ct);
        }
    }
}
