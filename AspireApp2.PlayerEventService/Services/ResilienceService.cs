using AspireApp2.PlayerEventService.Configuration;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Bulkhead;
using Polly.CircuitBreaker;
using Polly.Retry;
using Polly.Timeout;
using System.Diagnostics;
using System.Diagnostics.Metrics;
using System.Threading.RateLimiting;

namespace AspireApp2.PlayerEventService.Services;

public class ResilienceService : IResilienceService
{
    private readonly ResiliencePipeline _circuitBreakerPipeline;
    private readonly ResiliencePipeline _combinedPipeline;
    private readonly ResiliencePipeline _bulkheadPipeline;
    private readonly ResiliencePipeline _timeoutPipeline;
    private readonly ILogger<ResilienceService> _logger;
    private readonly ResiliencePipeline _retryPipeline;
    private readonly Meter _meter;
    private readonly Counter<int> _operationCounter;
    private readonly Counter<int> _failureCounter;
    private readonly Histogram<double> _operationDuration;
    private readonly ActivitySource _activitySource;

    public ResilienceService(IOptions<KafkaSettings> kafkaSettings, ILogger<ResilienceService> logger)
    {
        _logger = logger;
        ResilienceSettings settings = kafkaSettings.Value.Resilience;

        // Initialize metrics and activity source
        _meter = new Meter("AspireApp2.Resilience");
        _operationCounter = _meter.CreateCounter<int>("resilience_operations_total");
        _failureCounter = _meter.CreateCounter<int>("resilience_failures_total");
        _operationDuration = _meter.CreateHistogram<double>("resilience_operation_duration_seconds");
        _activitySource = new ActivitySource("AspireApp2.Resilience");

        // Create timeout pipeline
        _timeoutPipeline = new ResiliencePipelineBuilder()
            .AddTimeout(new TimeoutStrategyOptions
            {
                Timeout = TimeSpan.FromSeconds(30),
                OnTimeout = args =>
                {
                    _logger.LogWarning("Operation timed out after {Timeout}s", 30);
                    _failureCounter.Add(1, new KeyValuePair<string, object?>("reason", "timeout"));
                    return ValueTask.CompletedTask;
                }
            })
            .Build();

        // Create bulkhead pipeline for resource isolation
        _bulkheadPipeline = new ResiliencePipelineBuilder()
            .AddConcurrencyLimiter(new ConcurrencyLimiterOptions
            {
                PermitLimit = 10, // Max concurrent operations
                QueueLimit = 100, // Max queued operations
            })
            .Build();

        // Create retry pipeline with jitter and exponential backoff
        _retryPipeline = new ResiliencePipelineBuilder()
            .AddRetry(new RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                MaxRetryAttempts = settings.MaxRetryAttempts,
                Delay = TimeSpan.FromMilliseconds(settings.BaseDelayMs),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxDelay = TimeSpan.FromMilliseconds(settings.MaxDelayMs),
                OnRetry = args =>
                {
                    _logger.LogWarning("Retry attempt {AttemptNumber} for operation. Exception: {Exception}",
                        args.AttemptNumber, args.Outcome.Exception?.Message);
                    _failureCounter.Add(1, new KeyValuePair<string, object?>("reason", "retry"));
                    return ValueTask.CompletedTask;
                }
            })
            .Build();

        // Create circuit breaker pipeline with enhanced monitoring
        _circuitBreakerPipeline = new ResiliencePipelineBuilder()
            .AddCircuitBreaker(new CircuitBreakerStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                FailureRatio = 0.5,
                MinimumThroughput = settings.CircuitBreakerFailureThreshold,
                SamplingDuration = TimeSpan.FromSeconds(30),
                BreakDuration = TimeSpan.FromSeconds(settings.CircuitBreakerTimeoutSeconds),
                OnOpened = args =>
                {
                    _logger.LogError("Circuit breaker opened due to {Exception}", args.Outcome.Exception?.Message);
                    _failureCounter.Add(1, new KeyValuePair<string, object?>("reason", "circuit_open"));
                    return ValueTask.CompletedTask;
                },
                OnClosed = args =>
                {
                    _logger.LogInformation("Circuit breaker closed");
                    return ValueTask.CompletedTask;
                },
                OnHalfOpened = args =>
                {
                    _logger.LogInformation("Circuit breaker half-opened");
                    return ValueTask.CompletedTask;
                }
            })
            .Build();

        // Create comprehensive combined pipeline
        _combinedPipeline = new ResiliencePipelineBuilder()
            .AddTimeout(new TimeoutStrategyOptions
            {
                Timeout = TimeSpan.FromSeconds(30)
            })
            .AddConcurrencyLimiter(new ConcurrencyLimiterOptions
            {
                PermitLimit = 10,
                QueueLimit = 100
            })
            .AddRetry(new RetryStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                MaxRetryAttempts = settings.MaxRetryAttempts,
                Delay = TimeSpan.FromMilliseconds(settings.BaseDelayMs),
                BackoffType = DelayBackoffType.Exponential,
                UseJitter = true,
                MaxDelay = TimeSpan.FromMilliseconds(settings.MaxDelayMs)
            })
            .AddCircuitBreaker(new CircuitBreakerStrategyOptions
            {
                ShouldHandle = new PredicateBuilder().Handle<Exception>(),
                FailureRatio = 0.5,
                MinimumThroughput = settings.CircuitBreakerFailureThreshold,
                SamplingDuration = TimeSpan.FromSeconds(30),
                BreakDuration = TimeSpan.FromSeconds(settings.CircuitBreakerTimeoutSeconds)
            })
            .Build();
    }

    public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default)
    {
        using var activity = _activitySource.StartActivity($"Resilience.{operationName}");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _operationCounter.Add(1,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"));
            var result = await _retryPipeline.ExecuteAsync(async ct => await operation(), cancellationToken);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"),
                new KeyValuePair<string, object?>("status", "success"));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {OperationName} failed after all retry attempts", operationName);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"),
                new KeyValuePair<string, object?>("status", "failure"));
            throw;
        }
    }

    public async Task ExecuteWithRetryAsync(Func<Task> operation, string operationName,
        CancellationToken cancellationToken = default)
    {
        using var activity = _activitySource.StartActivity($"Resilience.{operationName}");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _operationCounter.Add(1,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"));
            await _retryPipeline.ExecuteAsync(async ct => await operation(), cancellationToken);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"),
                new KeyValuePair<string, object?>("status", "success"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {OperationName} failed after all retry attempts", operationName);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "retry"),
                new KeyValuePair<string, object?>("status", "failure"));
            throw;
        }
    }

    public async Task<T> ExecuteWithCircuitBreakerAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default)
    {
        using var activity = _activitySource.StartActivity($"Resilience.{operationName}");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _operationCounter.Add(1,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"));
            var result = await _circuitBreakerPipeline.ExecuteAsync(async ct => await operation(), cancellationToken);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"),
                new KeyValuePair<string, object?>("status", "success"));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {OperationName} failed with circuit breaker", operationName);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"),
                new KeyValuePair<string, object?>("status", "failure"));
            throw;
        }
    }

    public async Task ExecuteWithCircuitBreakerAsync(Func<Task> operation, string operationName,
        CancellationToken cancellationToken = default)
    {
        using var activity = _activitySource.StartActivity($"Resilience.{operationName}");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _operationCounter.Add(1,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"));
            await _circuitBreakerPipeline.ExecuteAsync(async ct => await operation(), cancellationToken);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"),
                new KeyValuePair<string, object?>("status", "success"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {OperationName} failed with circuit breaker", operationName);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "circuit_breaker"),
                new KeyValuePair<string, object?>("status", "failure"));
            throw;
        }
    }

    public async Task<T> ExecuteWithFullResilienceAsync<T>(Func<Task<T>> operation, string operationName,
        CancellationToken cancellationToken = default)
    {
        using var activity = _activitySource.StartActivity($"Resilience.{operationName}");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _operationCounter.Add(1,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "full_resilience"));
            var result = await _combinedPipeline.ExecuteAsync(async ct => await operation(), cancellationToken);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "full_resilience"),
                new KeyValuePair<string, object?>("status", "success"));
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Operation {OperationName} failed with full resilience pipeline", operationName);
            _operationDuration.Record(stopwatch.Elapsed.TotalSeconds,
                new KeyValuePair<string, object?>("operation", operationName),
                new KeyValuePair<string, object?>("type", "full_resilience"),
                new KeyValuePair<string, object?>("status", "failure"));
            throw;
        }
    }

    public async Task<T> ExecuteWithFallbackAsync<T>(Func<Task<T>> operation, Func<Task<T>> fallbackOperation,
        string operationName, CancellationToken cancellationToken = default)
    {
        try
        {
            return await ExecuteWithFullResilienceAsync(operation, operationName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Primary operation {OperationName} failed, executing fallback", operationName);
            return await fallbackOperation();
        }
    }

    public void Dispose()
    {
        _meter?.Dispose();
        _activitySource?.Dispose();
    }
}
