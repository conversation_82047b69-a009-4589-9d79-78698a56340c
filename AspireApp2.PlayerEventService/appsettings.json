{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "AspireApp2.PlayerEventService": "Information", "Confluent.Kafka": "Warning"}}, "AllowedHosts": "*", "MongoDbSettings": {"DatabaseName": "PlayerEvents", "CollectionName": "PlayerEvents", "ConnectionString": "mongodb://localhost:27017"}, "KafkaSettings": {"BootstrapServers": "localhost:9092", "EnableConsumer": true, "Consumer": {"GroupId": "player-event-consumer-group", "Topics": ["players", "player-events"], "AutoOffsetReset": "Earliest", "EnableAutoCommit": false, "SessionTimeoutMs": 30000, "MaxPollIntervalMs": 300000, "ConsumeTimeoutSeconds": 1, "SecurityProtocol": "Plaintext", "ApiVersionRequest": true}, "Producer": {"Acks": "All", "EnableIdempotence": true, "MessageTimeoutMs": 30000, "RetryBackoffMs": 100, "MessageSendMaxRetries": 3, "CompressionType": "Snappy", "SecurityProtocol": "Plaintext"}, "Resilience": {"MaxRetryAttempts": 5, "BaseDelayMs": 1000, "BackoffMultiplier": 2.0, "MaxDelayMs": 30000, "CircuitBreakerFailureThreshold": 10, "CircuitBreakerTimeoutSeconds": 60, "EnableDeadLetterQueue": true, "DeadLetterTopic": "players-dlq"}}, "MessageProcessing": {"MaxConcurrentMessages": 10, "BatchSize": 100, "ProcessingTimeoutSeconds": 300, "EnableDuplicateDetection": true, "EnableMetrics": true, "EnableTracing": true}, "HealthChecks": {"KafkaTimeoutSeconds": 10, "MongoDbTimeoutSeconds": 5, "MemoryThresholdMB": 512, "DiskSpaceThresholdGB": 2}, "DeadLetterQueue": {"RetryPolicy": {"MaxRetryAttempts": 3, "BaseDelaySeconds": 60, "MaxDelaySeconds": 3600, "BackoffMultiplier": 2.0}, "ProcessingSchedule": {"IntervalMinutes": 30, "BatchSize": 10, "Enabled": true}}, "Observability": {"EnableDetailedLogging": true, "EnablePerformanceCounters": true, "MetricsCollectionInterval": "00:00:30", "TracingSamplingRatio": 0.1}}