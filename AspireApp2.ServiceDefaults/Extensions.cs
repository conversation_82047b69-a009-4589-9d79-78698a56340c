using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Polly;
using Polly.CircuitBreaker;
using Polly.Retry;
using Polly.Timeout;
using Microsoft.Extensions.Http.Resilience;

namespace AspireApp2.ServiceDefaults;

// Adds common .NET Aspire services: service discovery, resilience, health checks, and OpenTelemetry.
// This project should be referenced by each service project in your solution.
// To learn more about using this project, see https://aka.ms/dotnet/aspire/service-defaults
public static class Extensions
{
    private const string HealthEndpointPath = "/health";
    private const string ReadinessEndpointPath = "/health/ready";
    private const string LivenessEndpointPath = "/health/live";
    private const string AlivenessEndpointPath = "/alive";

    public static TBuilder AddServiceDefaults<TBuilder>(this TBuilder builder) where TBuilder : IHostApplicationBuilder
    {
        builder.ConfigureOpenTelemetry();

        builder.AddDefaultHealthChecks();

        builder.AddResilienceDefaults();

        builder.Services.AddServiceDiscovery();

        builder.Services.ConfigureHttpClientDefaults(http =>
        {
            // Turn on resilience by default with enhanced configuration
            http.AddStandardResilienceHandler(options =>
            {
                // Configure retry options
                options.Retry.MaxRetryAttempts = 3;
                options.Retry.Delay = TimeSpan.FromSeconds(1);
                options.Retry.BackoffType = DelayBackoffType.Exponential;
                options.Retry.UseJitter = true;
                options.Retry.MaxDelay = TimeSpan.FromSeconds(30);

                // Configure circuit breaker options
                options.CircuitBreaker.FailureRatio = 0.5;
                options.CircuitBreaker.MinimumThroughput = 5;
                options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(60);

                // Configure timeout options
                options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(10);
                options.TotalRequestTimeout.Timeout = TimeSpan.FromMinutes(2);
            });

            // Turn on service discovery by default
            http.AddServiceDiscovery();
        });

        return builder;
    }

    public static TBuilder AddResilienceDefaults<TBuilder>(this TBuilder builder)
        where TBuilder : IHostApplicationBuilder
    {
        // Add resilience pipeline for database operations
        builder.Services.AddResiliencePipeline("database", pipelineBuilder =>
        {
            pipelineBuilder
                .AddTimeout(TimeSpan.FromSeconds(30))
                .AddRetry(new RetryStrategyOptions
                {
                    MaxRetryAttempts = 3,
                    Delay = TimeSpan.FromMilliseconds(500),
                    BackoffType = DelayBackoffType.Exponential,
                    UseJitter = true,
                    MaxDelay = TimeSpan.FromSeconds(10)
                })
                .AddCircuitBreaker(new CircuitBreakerStrategyOptions
                {
                    FailureRatio = 0.6,
                    MinimumThroughput = 3,
                    SamplingDuration = TimeSpan.FromSeconds(20),
                    BreakDuration = TimeSpan.FromSeconds(30)
                });
        });

        // Add resilience pipeline for external API calls
        builder.Services.AddResiliencePipeline("external-api", pipelineBuilder =>
        {
            pipelineBuilder
                .AddTimeout(TimeSpan.FromSeconds(60))
                .AddRetry(new RetryStrategyOptions
                {
                    MaxRetryAttempts = 5,
                    Delay = TimeSpan.FromSeconds(1),
                    BackoffType = DelayBackoffType.Exponential,
                    UseJitter = true,
                    MaxDelay = TimeSpan.FromMinutes(1)
                })
                .AddCircuitBreaker(new CircuitBreakerStrategyOptions
                {
                    FailureRatio = 0.7,
                    MinimumThroughput = 5,
                    SamplingDuration = TimeSpan.FromSeconds(60),
                    BreakDuration = TimeSpan.FromMinutes(2)
                });
        });

        // Add resilience pipeline for message processing
        builder.Services.AddResiliencePipeline("message-processing", pipelineBuilder =>
        {
            pipelineBuilder
                .AddTimeout(TimeSpan.FromMinutes(5))
                .AddRetry(new RetryStrategyOptions
                {
                    MaxRetryAttempts = 3,
                    Delay = TimeSpan.FromSeconds(2),
                    BackoffType = DelayBackoffType.Linear,
                    UseJitter = true
                })
                .AddCircuitBreaker(new CircuitBreakerStrategyOptions
                {
                    FailureRatio = 0.8,
                    MinimumThroughput = 2,
                    SamplingDuration = TimeSpan.FromMinutes(1),
                    BreakDuration = TimeSpan.FromMinutes(5)
                });
        });

        return builder;
    }

    public static TBuilder ConfigureOpenTelemetry<TBuilder>(this TBuilder builder)
        where TBuilder : IHostApplicationBuilder
    {
        builder.Logging.AddOpenTelemetry(logging =>
        {
            logging.IncludeFormattedMessage = true;
            logging.IncludeScopes = true;
        });

        builder.Services.AddOpenTelemetry()
            .WithMetrics(metrics =>
            {
                metrics.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddMeter("AspireApp2.*") // Add custom meters
                    .AddView("http_request_duration_seconds", 
                        new ExplicitBucketHistogramConfiguration 
                        { 
                            Boundaries = [0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5, 7.5, 10] 
                        });
            })
            .WithTracing(tracing =>
            {
                tracing.AddSource(builder.Environment.ApplicationName)
                    .AddSource("AspireApp2.*") // Add custom trace sources
                    .AddAspNetCoreInstrumentation(tracing =>
                        // Exclude health check requests from tracing
                        tracing.Filter = context =>
                            !context.Request.Path.StartsWithSegments(HealthEndpointPath)
                            && !context.Request.Path.StartsWithSegments(ReadinessEndpointPath)
                            && !context.Request.Path.StartsWithSegments(LivenessEndpointPath)
                            && !context.Request.Path.StartsWithSegments(AlivenessEndpointPath)
                    )
                    .AddHttpClientInstrumentation();
            });

        builder.AddOpenTelemetryExporters();

        return builder;
    }

    private static TBuilder AddOpenTelemetryExporters<TBuilder>(this TBuilder builder)
        where TBuilder : IHostApplicationBuilder
    {
        bool useOtlpExporter = !string.IsNullOrWhiteSpace(builder.Configuration["OTEL_EXPORTER_OTLP_ENDPOINT"]);

        if (useOtlpExporter)
        {
            builder.Services.AddOpenTelemetry().UseOtlpExporter();
        }

        return builder;
    }

    public static TBuilder AddDefaultHealthChecks<TBuilder>(this TBuilder builder)
        where TBuilder : IHostApplicationBuilder
    {
        builder.Services.AddHealthChecks()
            // Add a default liveness check to ensure app is responsive
            .AddCheck("self", () => HealthCheckResult.Healthy("Service is alive"), ["live"])
            // Add readiness checks
            .AddCheck("startup", () => HealthCheckResult.Healthy("Service startup completed"), ["ready"])
            // Add dependency checks with appropriate tags
            .AddCheck("memory", () =>
            {
                var allocatedBytes = GC.GetTotalMemory(false);
                var memoryLimitBytes = 1024L * 1024L * 1024L; // 1GB limit example
                
                if (allocatedBytes > memoryLimitBytes)
                {
                    return HealthCheckResult.Degraded($"High memory usage: {allocatedBytes / 1024 / 1024} MB");
                }
                
                return HealthCheckResult.Healthy($"Memory usage: {allocatedBytes / 1024 / 1024} MB");
            }, ["live", "ready"])
            .AddCheck("disk_space", () =>
            {
                try
                {
                    var drive = new DriveInfo(Path.GetPathRoot(Environment.CurrentDirectory)!);
                    var freeSpaceGB = drive.AvailableFreeSpace / 1024 / 1024 / 1024;
                    
                    if (freeSpaceGB < 1) // Less than 1GB free
                    {
                        return HealthCheckResult.Unhealthy($"Low disk space: {freeSpaceGB} GB remaining");
                    }
                    
                    if (freeSpaceGB < 5) // Less than 5GB free
                    {
                        return HealthCheckResult.Degraded($"Disk space low: {freeSpaceGB} GB remaining");
                    }
                    
                    return HealthCheckResult.Healthy($"Disk space: {freeSpaceGB} GB available");
                }
                catch (Exception ex)
                {
                    return HealthCheckResult.Unhealthy($"Unable to check disk space: {ex.Message}");
                }
            }, ["ready"]);

        return builder;
    }

    public static WebApplication MapDefaultEndpoints(this WebApplication app)
    {
        // Adding health checks endpoints to applications in non-development environments has security implications.
        // See https://aka.ms/dotnet/aspire/healthchecks for details before enabling these endpoints in non-development environments.
        if (app.Environment.IsDevelopment())
        {
            // Comprehensive health check with detailed response
            app.MapHealthChecks(HealthEndpointPath, new HealthCheckOptions
            {
                ResponseWriter = WriteHealthCheckResponse,
                AllowCachingResponses = false
            });

            // Readiness check - all dependencies must be healthy
            app.MapHealthChecks(ReadinessEndpointPath, new HealthCheckOptions 
            { 
                Predicate = check => check.Tags.Contains("ready"),
                ResponseWriter = WriteHealthCheckResponse,
                AllowCachingResponses = false
            });

            // Liveness check - only basic service health
            app.MapHealthChecks(LivenessEndpointPath, new HealthCheckOptions 
            { 
                Predicate = check => check.Tags.Contains("live"),
                ResponseWriter = WriteHealthCheckResponse,
                AllowCachingResponses = false
            });

            // Simple alive check for load balancers
            app.MapHealthChecks(AlivenessEndpointPath, new HealthCheckOptions 
            { 
                Predicate = r => r.Tags.Contains("live"),
                ResponseWriter = (context, report) =>
                {
                    context.Response.ContentType = "text/plain";
                    return context.Response.WriteAsync(report.Status == HealthStatus.Healthy ? "Healthy" : "Unhealthy");
                }
            });
        }

        return app;
    }

    private static async Task WriteHealthCheckResponse(HttpContext context, HealthReport report)
    {
        var response = new
        {
            status = report.Status.ToString(),
            duration = report.TotalDuration,
            checks = report.Entries.Select(pair => new
            {
                name = pair.Key,
                status = pair.Value.Status.ToString(),
                description = pair.Value.Description,
                duration = pair.Value.Duration,
                exception = pair.Value.Exception?.Message,
                data = pair.Value.Data.Count > 0 ? pair.Value.Data : null,
                tags = pair.Value.Tags
            }),
            timestamp = DateTime.UtcNow
        };

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(response, new JsonSerializerOptions 
        { 
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        }));
    }
}
