#!/usr/bin/env pwsh

Write-Host "=== AspireApp2 Connection Diagnostics ===" -ForegroundColor Cyan

# Function to test port connectivity
function Test-Port {
    param($HostName, $Port, $Service)
    try {
        $connection = Test-NetConnection -ComputerName $HostName -Port $Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✅ $Service ($HostName`:$Port) - Connected" -ForegroundColor Green
        } else {
            Write-Host "❌ $Service ($HostName`:$Port) - Connection Failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $Service ($HostName`:$Port) - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to check HTTP health endpoints
function Test-HealthEndpoint {
    param($Url, $Service)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $Service Health Check - OK" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $Service Health Check - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $Service Health Check - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nChecking Infrastructure Services..." -ForegroundColor Yellow

# Check common Kafka ports
Test-Port "localhost" 9092 "Kafka (Default)"
Test-Port "localhost" 9093 "Kafka (SSL)"

# Check common Redis ports
Test-Port "localhost" 6379 "Redis (Default)"

# Check SQL Server
Test-Port "localhost" 1433 "SQL Server (Default)"

# Check MongoDB
Test-Port "localhost" 27017 "MongoDB (Default)"

Write-Host "`nChecking Application Services..." -ForegroundColor Yellow

# Check if services are running (common development ports)
$commonPorts = @(
    @{Port=5000; Service="API (HTTP)"},
    @{Port=5001; Service="API (HTTPS)"},
    @{Port=7000; Service="Web (HTTP)"},
    @{Port=7001; Service="Web (HTTPS)"},
    @{Port=8080; Service="PlayerEventService (HTTP)"},
    @{Port=8081; Service="PlayerEventService (HTTPS)"}
)

foreach ($portInfo in $commonPorts) {
    Test-Port "localhost" $portInfo.Port $portInfo.Service
}

Write-Host "`nChecking Health Endpoints..." -ForegroundColor Yellow

# Check health endpoints (try common patterns)
$healthEndpoints = @(
    "http://localhost:5000/health",
    "http://localhost:7000/health",
    "http://localhost:8080/health",
    "https://localhost:5001/health",
    "https://localhost:7001/health",
    "https://localhost:8081/health"
)

foreach ($endpoint in $healthEndpoints) {
    Test-HealthEndpoint $endpoint "Service"
}

Write-Host "`n=== Diagnostic Complete ===" -ForegroundColor Cyan
Write-Host "If services are not running, start with: dotnet run --project AspireApp2.AppHost" -ForegroundColor Gray 