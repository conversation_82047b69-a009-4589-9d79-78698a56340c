using System.Net;
using System.Text;
using System.Text.Json;
using AspireApp2.Web.Models;
using Microsoft.Extensions.Caching.Memory;

namespace AspireApp2.Web.Services;

public interface IPlayerService
{
    Task<IEnumerable<PlayerDto>> GetPlayersAsync(string? team = null);
    Task<PlayerDto?> GetPlayerAsync(int id);
    Task<PlayerDto> CreatePlayerAsync(CreatePlayerRequest request);
    Task InvalidateCache();
}

public class PlayerService : IPlayerService
{
    private const string PLAYERS_CACHE_KEY = "players_list";
    private const string PLAYER_CACHE_KEY_PREFIX = "player_";
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly ILogger<PlayerService> _logger;

    public PlayerService(HttpClient httpClient, ILogger<PlayerService> logger, IMemoryCache cache)
    {
        _httpClient = httpClient;
        _logger = logger;
        _cache = cache;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase, PropertyNameCaseInsensitive = true
        };

        // Log the base address for debugging
        _logger.LogInformation("PlayerService initialized with base address: {BaseAddress}",
            _httpClient.BaseAddress?.ToString() ?? "null");
    }

    public async Task<IEnumerable<PlayerDto>> GetPlayersAsync(string? team = null)
    {
        try
        {
            // Check cache first
            string cacheKey = $"{PLAYERS_CACHE_KEY}_{team ?? "all"}";
            if (_cache.TryGetValue(cacheKey, out IEnumerable<PlayerDto>? cachedPlayers))
            {
                _logger.LogDebug("Retrieved {Count} players from cache for team: {Team}",
                    cachedPlayers?.Count() ?? 0, team ?? "All teams");
                return cachedPlayers ?? [];
            }

            // Ensure the base address is properly configured
            await EnsureBaseAddressAsync();

            string url = "api/players";
            if (!string.IsNullOrEmpty(team))
            {
                url += $"?team={Uri.EscapeDataString(team)}";
            }

            _logger.LogDebug("Making request to: {BaseAddress}{Url}", _httpClient.BaseAddress, url);
            HttpResponseMessage response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();

            string json = await response.Content.ReadAsStringAsync();
            IEnumerable<PlayerDto> players =
                JsonSerializer.Deserialize<IEnumerable<PlayerDto>>(json, _jsonOptions) ?? [];

            // Cache the result
            _cache.Set(cacheKey, players, _cacheExpiration);

            _logger.LogInformation("Retrieved {Count} players for team: {Team}",
                players.Count(), team ?? "All teams");

            return players;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving players for team: {Team}", team);
            throw;
        }
    }

    public async Task<PlayerDto?> GetPlayerAsync(int id)
    {
        try
        {
            // Check cache first
            string cacheKey = $"{PLAYER_CACHE_KEY_PREFIX}{id}";
            if (_cache.TryGetValue(cacheKey, out PlayerDto? cachedPlayer))
            {
                _logger.LogDebug("Retrieved player {PlayerId} from cache", id);
                return cachedPlayer;
            }

            // Ensure the base address is properly configured
            await EnsureBaseAddressAsync();

            _logger.LogDebug("Making request to: {BaseAddress}api/players/{Id}", _httpClient.BaseAddress, id);
            HttpResponseMessage response = await _httpClient.GetAsync($"api/players/{id}");

            if (response.StatusCode == HttpStatusCode.NotFound)
            {
                return null;
            }

            response.EnsureSuccessStatusCode();

            string json = await response.Content.ReadAsStringAsync();
            PlayerDto? player = JsonSerializer.Deserialize<PlayerDto>(json, _jsonOptions);

            // Cache the result
            if (player != null)
            {
                _cache.Set(cacheKey, player, _cacheExpiration);
            }

            _logger.LogInformation("Retrieved player {PlayerId}", id);

            return player;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving player {PlayerId}", id);
            throw;
        }
    }

    public async Task<PlayerDto> CreatePlayerAsync(CreatePlayerRequest request)
    {
        try
        {
            // Ensure the base address is properly configured
            await EnsureBaseAddressAsync();

            var command = new
            {
                firstName = request.FirstName,
                lastName = request.LastName,
                email = request.Email,
                position = request.Position,
                jerseyNumber = request.JerseyNumber,
                dateOfBirth = request.DateOfBirth,
                team = request.Team,
                salary = request.Salary
            };

            string json = JsonSerializer.Serialize(command, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogDebug("Making POST request to: {BaseAddress}api/players", _httpClient.BaseAddress);
            HttpResponseMessage response = await _httpClient.PostAsync("api/players", content);

            if (!response.IsSuccessStatusCode)
            {
                string errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to create player. Status: {StatusCode}, Error: {Error}",
                    response.StatusCode, errorContent);

                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    // Try to parse the error message
                    try
                    {
                        JsonElement errorObj = JsonSerializer.Deserialize<JsonElement>(errorContent);
                        if (errorObj.TryGetProperty("error", out JsonElement errorProp))
                        {
                            throw new InvalidOperationException(errorProp.GetString());
                        }
                    }
                    catch (JsonException)
                    {
                        // If we can't parse the error, throw a generic message
                    }

                    throw new InvalidOperationException("Invalid player data provided.");
                }

                response.EnsureSuccessStatusCode();
            }

            string responseJson = await response.Content.ReadAsStringAsync();
            PlayerDto? createdPlayer = JsonSerializer.Deserialize<PlayerDto>(responseJson, _jsonOptions);

            // Invalidate cache after creating a new player
            await InvalidateCache();

            _logger.LogInformation("Created player {PlayerId} - {PlayerName}",
                createdPlayer?.Id, $"{request.FirstName} {request.LastName}");

            return createdPlayer ?? throw new InvalidOperationException("Failed to deserialize created player");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating player: {FirstName} {LastName}",
                request.FirstName, request.LastName);
            throw;
        }
    }

    public Task InvalidateCache()
    {
        // Remove all player-related cache entries
        var keysToRemove = new List<string>();

        // This is a simplified approach - in production you might want to use a more sophisticated cache key management
        _cache.Remove($"{PLAYERS_CACHE_KEY}_all");

        // You could also implement a more comprehensive cache invalidation strategy here
        _logger.LogDebug("Invalidated player cache");

        return Task.CompletedTask;
    }

    private async Task EnsureBaseAddressAsync()
    {
        // If base address is null, wait a bit for service discovery to resolve
        if (_httpClient.BaseAddress == null)
        {
            _logger.LogWarning("HttpClient BaseAddress is null, waiting for service discovery...");

            // Increased wait time for service discovery during startup
            var maxWait = TimeSpan.FromSeconds(15); // Increased to allow for service startup
            DateTime start = DateTime.UtcNow;

            while (_httpClient.BaseAddress == null && DateTime.UtcNow - start < maxWait)
            {
                await Task.Delay(100); // Reduced delay for faster checking
            }

            if (_httpClient.BaseAddress == null)
            {
                // As a fallback, try to set a direct URL if we're in development
                bool isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
                if (isDevelopment)
                {
                    _logger.LogWarning("Service discovery failed, attempting fallback to direct API URL...");
                    try
                    {
                        // Try common development URLs
                        string[] fallbackUrls = new[] { "https://localhost:7463", "http://localhost:5377" };

                        foreach (string url in fallbackUrls)
                        {
                            try
                            {
                                using var testClient = new HttpClient
                                {
                                    BaseAddress = new Uri(url), Timeout = TimeSpan.FromSeconds(2)
                                };
                                HttpResponseMessage response = await testClient.GetAsync("/health");
                                if (response.IsSuccessStatusCode)
                                {
                                    _httpClient.BaseAddress = new Uri(url);
                                    _logger.LogWarning("Using fallback URL: {Url}", url);
                                    return;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogDebug("Fallback URL {Url} failed: {Error}", url, ex.Message);
                            }
                        }

                        throw new InvalidOperationException(
                            "Service discovery failed and no fallback API URL is reachable. Please ensure the API service is running.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to establish fallback connection");
                        throw;
                    }
                }

                throw new InvalidOperationException(
                    "Service discovery failed. Please check that the API service is properly configured and running.");
            }
        }
    }
}
