{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=AspireApp2;Trusted_Connection=true;MultipleActiveResultSets=true;"}, "OutboxProcessor": {"PollingInterval": "00:00:05", "BatchSize": 50, "MaxRetryAttempts": 3, "BaseRetryDelay": "00:00:01", "MaxRetryDelay": "00:05:00", "EnableMetrics": true}, "Resilience": {"Database": {"MaxRetryAttempts": 3, "BaseDelayMs": 500, "MaxDelayMs": 10000, "CircuitBreakerFailureThreshold": 3, "CircuitBreakerTimeoutSeconds": 30, "TimeoutSeconds": 30}, "ExternalApi": {"MaxRetryAttempts": 5, "BaseDelayMs": 1000, "MaxDelayMs": 60000, "CircuitBreakerFailureThreshold": 5, "CircuitBreakerTimeoutSeconds": 120, "TimeoutSeconds": 60}, "MessageProcessing": {"MaxRetryAttempts": 3, "BaseDelayMs": 2000, "MaxDelayMs": 300000, "CircuitBreakerFailureThreshold": 2, "CircuitBreakerTimeoutSeconds": 300, "TimeoutSeconds": 300}}, "HealthChecks": {"MemoryThresholdMB": 1024, "DiskSpaceThresholdGB": 5, "TimeoutSeconds": 30}, "Observability": {"EnableMetrics": true, "EnableTracing": true, "EnableLogging": true, "SamplingRatio": 0.1}}