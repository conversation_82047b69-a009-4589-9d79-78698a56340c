<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Confluent.Kafka" Version="9.3.1"/>
    <PackageReference Include="AutoMapper" Version="14.0.0"/>
    <PackageReference Include="Confluent.Kafka" Version="2.10.1"/>
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1"/>
    <PackageReference Include="MediatR" Version="12.5.0"/>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5"/>
    <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.3.1"/>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AspireApp2.Api.Application\AspireApp2.Api.Application.csproj"/>
    <ProjectReference Include="..\AspireApp2.Api.Infrastructure\AspireApp2.Api.Infrastructure.csproj"/>
    <ProjectReference Include="..\..\..\AspireApp2.ServiceDefaults\AspireApp2.ServiceDefaults.csproj"/>
  </ItemGroup>

</Project>
