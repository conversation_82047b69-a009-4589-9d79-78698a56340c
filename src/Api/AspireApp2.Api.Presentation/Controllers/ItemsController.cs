using System.Net;
using AspireApp2.Api.Application.Commands.Items;
using AspireApp2.Api.Application.DTOs;
using AspireApp2.Api.Application.Queries.Items;
using AspireApp2.Api.Domain.Entities;
using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace AspireApp2.Api.Presentation.Controllers;

/// <summary>
///     Controller for managing items
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Tags("Items")]
public class ItemsController : ControllerBase
{
    private readonly ILogger<ItemsController> _logger;
    private readonly IMapper _mapper;
    private readonly IMediator _mediator;

    public ItemsController(IMediator mediator, IMapper mapper, ILogger<ItemsController> logger)
    {
        _mediator = mediator;
        _mapper = mapper;
        _logger = logger;
    }

    /// <summary>
    ///     Get all items
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns>List of items</returns>
    /// <response code="200">Returns the list of items</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ItemDto>), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<IEnumerable<ItemDto>>> GetItems(CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetItemsQuery();
            IEnumerable<Item> items = await _mediator.Send(query, cancellationToken);

            IEnumerable<ItemDto> itemDtos = _mapper.Map<IEnumerable<ItemDto>>(items);

            _logger.LogInformation("Retrieved {Count} items", itemDtos.Count());

            return Ok(itemDtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving items");
            return StatusCode(500, "An error occurred while retrieving items");
        }
    }

    /// <summary>
    ///     Get an item by ID
    /// </summary>
    /// <param name="id">Item ID</param>
    /// <param name="cancellationToken"></param>
    /// <returns>Item details</returns>
    /// <response code="200">Returns the item</response>
    /// <response code="404">If the item is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ItemDto), (int)HttpStatusCode.OK)]
    [ProducesResponseType((int)HttpStatusCode.NotFound)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ItemDto>> GetItem(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetItemByIdQuery(id);
            Item? item = await _mediator.Send(query, cancellationToken);

            if (item == null)
            {
                _logger.LogWarning("Item {ItemId} not found", id);
                return NotFound($"Item with ID {id} not found");
            }

            ItemDto itemDto = _mapper.Map<ItemDto>(item);

            _logger.LogInformation("Retrieved item {ItemId} - {ItemName}", item.Id, item.Name);

            return Ok(itemDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving item {ItemId}", id);
            return StatusCode(500, "An error occurred while retrieving the item");
        }
    }

    /// <summary>
    ///     Create a new item
    /// </summary>
    /// <param name="command">Item creation data</param>
    /// <param name="cancellationToken"></param>
    /// <returns>Created item</returns>
    /// <response code="201">Returns the newly created item</response>
    /// <response code="400">If the item data is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(ItemDto), (int)HttpStatusCode.Created)]
    [ProducesResponseType((int)HttpStatusCode.BadRequest)]
    [ProducesResponseType((int)HttpStatusCode.InternalServerError)]
    public async Task<ActionResult<ItemDto>> CreateItem(
        [FromBody] CreateItemCommand command,
        CancellationToken cancellationToken = default)
    {
        try
        {
            Item item = await _mediator.Send(command, cancellationToken);
            ItemDto itemDto = _mapper.Map<ItemDto>(item);

            _logger.LogInformation("Created item {ItemId} - {ItemName}", item.Id, item.Name);

            return CreatedAtAction(
                nameof(GetItem),
                new { id = item.Id },
                itemDto);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Business rule violation when creating item: {Error}", ex.Message);
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating item: {Name}", command.Name);
            return StatusCode(500, "An error occurred while creating the item");
        }
    }
}
