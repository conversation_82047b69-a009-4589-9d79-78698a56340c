namespace AspireApp2.Api.Application.DTOs;

/// <summary>
///     Data Transfer Object for Player
/// </summary>
public record PlayerDto(
    int Id,
    string FirstName,
    string LastName,
    string Email,
    string Position,
    int JerseyNumber,
    DateTime DateOfBirth,
    string Team,
    decimal Salary,
    string FullName,
    int Age,
    bool IsActive,
    DateTime CreatedAt,
    DateTime? UpdatedAt
);

/// <summary>
///     Response DTO for creating a player
/// </summary>
public record CreatePlayerResponse(
    int Id,
    string FirstName,
    string LastName,
    string Email,
    string Position,
    int JerseyNumber,
    DateTime DateOfBirth,
    string Team,
    decimal Salary,
    string FullName,
    int Age,
    DateTime CreatedAt
);
