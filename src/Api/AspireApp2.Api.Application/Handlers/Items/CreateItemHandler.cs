using AspireApp2.Api.Application.Commands.Items;
using AspireApp2.Api.Application.Interfaces;
using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Application.Handlers.Items;

/// <summary>
///     Handler for creating a new item
/// </summary>
public class CreateItemHandler : IRequestHandler<CreateItemCommand, Item>
{
    private readonly IItemRepository _itemRepository;
    private readonly ILogger<CreateItemHandler> _logger;
    private readonly IOutboxService _outboxService;
    private readonly IUnitOfWork _unitOfWork;

    public CreateItemHandler(
        IItemRepository itemRepository,
        IOutboxService outboxService,
        IUnitOfWork unitOfWork,
        ILogger<CreateItemHandler> logger)
    {
        _itemRepository = itemRepository;
        _outboxService = outboxService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Item> Handle(CreateItemCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating item: {Name}", request.Name);

        // Create item entity
        var item = new Item
        {
            Name = request.Name.Trim(),
            Description = request.Description?.Trim() ?? string.Empty,
            CreatedAt = DateTime.UtcNow
        };

        // Validate business rules
        if (!item.IsValidName())
        {
            throw new InvalidOperationException("Item name is invalid.");
        }

        if (!item.IsValidDescription())
        {
            throw new InvalidOperationException("Item description is too long.");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // Save item to database
            Item createdItem = await _itemRepository.CreateAsync(item, cancellationToken);

            // Create outbox message for event publishing
            await _outboxService.AddMessageAsync(
                "items",
                createdItem.Id.ToString(),
                new
                {
                    EventType = "ItemCreated",
                    ItemId = createdItem.Id,
                    Item = createdItem,
                    Timestamp = DateTime.UtcNow
                },
                cancellationToken);

            // Commit transaction
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Successfully created item {ItemId} - {ItemName}",
                createdItem.Id, createdItem.Name);

            return createdItem;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            _logger.LogError(ex, "Error creating item: {Name}", request.Name);
            throw;
        }
    }
}
