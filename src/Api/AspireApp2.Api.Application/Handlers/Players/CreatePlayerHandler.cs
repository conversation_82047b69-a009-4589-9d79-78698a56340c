using AspireApp2.Api.Application.Commands.Players;
using AspireApp2.Api.Application.Interfaces;
using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using AspireApp2.Api.Domain.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Application.Handlers.Players;

/// <summary>
///     Handler for creating a new player
/// </summary>
public class CreatePlayerHandler : IRequestHandler<CreatePlayerCommand, Player>
{
    private readonly ILogger<CreatePlayerHandler> _logger;
    private readonly IOutboxService _outboxService;
    private readonly IPlayerDomainService _playerDomainService;
    private readonly IPlayerRepository _playerRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreatePlayerHandler(
        IPlayerRepository playerRepository,
        IPlayerDomainService playerDomainService,
        IOutboxService outboxService,
        IUnitOfWork unitOfWork,
        ILogger<CreatePlayerHandler> logger)
    {
        _playerRepository = playerRepository;
        _playerDomainService = playerDomainService;
        _outboxService = outboxService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Player> Handle(CreatePlayerCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating player: {FirstName} {LastName} for team {Team}",
            request.FirstName, request.LastName, request.Team);

        // Create player entity
        var player = new Player
        {
            FirstName = request.FirstName.Trim(),
            LastName = request.LastName.Trim(),
            Email = request.Email.Trim().ToLowerInvariant(),
            Position = request.Position.Trim(),
            JerseyNumber = request.JerseyNumber,
            DateOfBirth = request.DateOfBirth,
            Team = request.Team.Trim(),
            Salary = request.Salary,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        // Validate business rules
        if (!await _playerDomainService.ValidatePlayerCreationAsync(player, cancellationToken))
        {
            throw new InvalidOperationException("Player validation failed.");
        }

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // Save player to database
            Player createdPlayer = await _playerRepository.CreateAsync(player, cancellationToken);

            // Create outbox message for event publishing
            await _outboxService.AddMessageAsync(
                "players",
                createdPlayer.Id.ToString(),
                new
                {
                    EventType = "PlayerCreated",
                    PlayerId = createdPlayer.Id,
                    Player = createdPlayer,
                    Timestamp = DateTime.UtcNow
                },
                cancellationToken);

            // Commit transaction
            await _unitOfWork.CommitTransactionAsync(cancellationToken);

            _logger.LogInformation("Successfully created player {PlayerId} - {PlayerName}",
                createdPlayer.Id, createdPlayer.FullName);

            return createdPlayer;
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            _logger.LogError(ex, "Error creating player: {FirstName} {LastName}", request.FirstName, request.LastName);
            throw;
        }
    }
}
