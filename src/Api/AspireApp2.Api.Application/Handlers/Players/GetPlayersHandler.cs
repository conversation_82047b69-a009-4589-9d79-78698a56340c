using AspireApp2.Api.Application.Queries.Players;
using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Application.Handlers.Players;

/// <summary>
///     Handler for getting players
/// </summary>
public class GetPlayersHandler : IRequestHandler<GetPlayersQuery, IEnumerable<Player>>
{
    private readonly ILogger<GetPlayersHandler> _logger;
    private readonly IPlayerRepository _playerRepository;

    public GetPlayersHandler(IPlayerRepository playerRepository, ILogger<GetPlayersHandler> logger)
    {
        _playerRepository = playerRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<Player>> Handle(GetPlayersQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving players for team: {Team}", request.Team ?? "All teams");

        try
        {
            if (!string.IsNullOrEmpty(request.Team))
            {
                return await _playerRepository.GetByTeamAsync(request.Team, cancellationToken);
            }

            return await _playerRepository.GetAllAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving players for team: {Team}", request.Team);
            throw;
        }
    }
}

/// <summary>
///     Handler for getting a player by ID
/// </summary>
public class GetPlayerByIdHandler : IRequestHandler<GetPlayerByIdQuery, Player?>
{
    private readonly ILogger<GetPlayerByIdHandler> _logger;
    private readonly IPlayerRepository _playerRepository;

    public GetPlayerByIdHandler(IPlayerRepository playerRepository, ILogger<GetPlayerByIdHandler> logger)
    {
        _playerRepository = playerRepository;
        _logger = logger;
    }

    public async Task<Player?> Handle(GetPlayerByIdQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Retrieving player with ID: {PlayerId}", request.Id);

        try
        {
            return await _playerRepository.GetByIdAsync(request.Id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving player with ID: {PlayerId}", request.Id);
            throw;
        }
    }
}
