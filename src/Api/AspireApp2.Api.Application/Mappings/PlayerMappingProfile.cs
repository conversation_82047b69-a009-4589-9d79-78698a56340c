using AspireApp2.Api.Application.DTOs;
using AspireApp2.Api.Domain.Entities;
using AutoMapper;

namespace AspireApp2.Api.Application.Mappings;

/// <summary>
///     AutoMapper profile for Player mappings
/// </summary>
public class PlayerMappingProfile : Profile
{
    public PlayerMappingProfile()
    {
        CreateMap<Player, PlayerDto>();
        CreateMap<Player, CreatePlayerResponse>();
    }
}
