namespace AspireApp2.Api.Application.Interfaces;

/// <summary>
///     Service interface for Kafka message production
/// </summary>
public interface IKafkaProducerService
{
    /// <summary>
    ///     Publishes a message to the specified Kafka topic
    /// </summary>
    /// <param name="topic">The Kafka topic to publish to</param>
    /// <param name="key">The message key</param>
    /// <param name="message">The message payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task PublishAsync(string topic, string key, string message, CancellationToken cancellationToken = default);
}
