<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\AspireApp2.Api.Domain\AspireApp2.Api.Domain.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0"/>
    <PackageReference Include="FluentValidation" Version="12.0.0"/>
    <PackageReference Include="MediatR" Version="12.5.0"/>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6"/>
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
