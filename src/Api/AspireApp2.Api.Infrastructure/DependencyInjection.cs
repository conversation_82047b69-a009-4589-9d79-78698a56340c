using AspireApp2.Api.Application.Interfaces;
using AspireApp2.Api.Domain.Repositories;
using AspireApp2.Api.Domain.Services;
using AspireApp2.Api.Infrastructure.Data;
using AspireApp2.Api.Infrastructure.Repositories;
using AspireApp2.Api.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AspireApp2.Api.Infrastructure;

/// <summary>
///     Dependency injection configuration for Infrastructure layer
/// </summary>
public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Add DbContext
        services.AddDbContext<ApiDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

        // Add Unit of Work
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Add Repositories
        services.AddScoped<IPlayerRepository, PlayerRepository>();
        services.AddScoped<IItemRepository, ItemRepository>();
        services.AddScoped<IOutboxRepository, OutboxRepository>();

        // Add Domain Services
        services.AddScoped<IPlayerDomainService, PlayerDomainService>();

        // Add Application Services
        services.AddScoped<IOutboxService, OutboxService>();
        services.AddScoped<IKafkaProducerService, KafkaProducerService>();

        // Add Background Services
        services.AddHostedService<OutboxProcessorService>();

        return services;
    }
}
