using AspireApp2.Api.Application.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Diagnostics.Metrics;
using System.Diagnostics;

namespace AspireApp2.Api.Infrastructure.Services;

/// <summary>
///     Background service for processing outbox messages with enhanced resilience
/// </summary>
public class OutboxProcessorService : BackgroundService
{
    private readonly ILogger<OutboxProcessorService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly Meter _meter;
    private readonly Counter<int> _messagesProcessedCounter;
    private readonly Counter<int> _messagesFailedCounter;
    private readonly Histogram<double> _processingDuration;
    private readonly ActivitySource _activitySource;

    // Configuration values
    private readonly TimeSpan _pollingInterval;
    private readonly int _batchSize;
    private readonly int _maxRetryAttempts;
    private readonly TimeSpan _baseRetryDelay;
    private readonly TimeSpan _maxRetryDelay;

    public OutboxProcessorService(
        IServiceProvider serviceProvider, 
        ILogger<OutboxProcessorService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _configuration = configuration;

        // Initialize metrics and tracing
        _meter = new Meter("AspireApp2.Api.OutboxProcessor");
        _messagesProcessedCounter = _meter.CreateCounter<int>("outbox_messages_processed_total");
        _messagesFailedCounter = _meter.CreateCounter<int>("outbox_messages_failed_total");
        _processingDuration = _meter.CreateHistogram<double>("outbox_processing_duration_seconds");
        _activitySource = new ActivitySource("AspireApp2.Api.OutboxProcessor");

        // Load configuration with defaults
        _pollingInterval = _configuration.GetValue<TimeSpan>("OutboxProcessor:PollingInterval", TimeSpan.FromSeconds(5));
        _batchSize = _configuration.GetValue<int>("OutboxProcessor:BatchSize", 50);
        _maxRetryAttempts = _configuration.GetValue<int>("OutboxProcessor:MaxRetryAttempts", 3);
        _baseRetryDelay = _configuration.GetValue<TimeSpan>("OutboxProcessor:BaseRetryDelay", TimeSpan.FromSeconds(1));
        _maxRetryDelay = _configuration.GetValue<TimeSpan>("OutboxProcessor:MaxRetryDelay", TimeSpan.FromMinutes(5));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Outbox processor service started with polling interval: {PollingInterval}, batch size: {BatchSize}", 
            _pollingInterval, _batchSize);

        var retryAttempt = 0;
        var consecutiveFailures = 0;

        while (!stoppingToken.IsCancellationRequested)
        {
            using var activity = _activitySource.StartActivity("ProcessOutboxBatch");
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using IServiceScope scope = _serviceProvider.CreateScope();
                IOutboxService outboxService = scope.ServiceProvider.GetRequiredService<IOutboxService>();

                bool hasProcessedMessages = await ProcessWithRetry(
                    () => outboxService.ProcessUnprocessedMessagesAsync(_batchSize, stoppingToken),
                    "ProcessOutboxMessages",
                    stoppingToken);

                if (hasProcessedMessages)
                {
                    _logger.LogDebug("Processed outbox messages, checking for more immediately");
                    consecutiveFailures = 0;
                    retryAttempt = 0;
                    _processingDuration.Record(stopwatch.Elapsed.TotalSeconds, 
                        new KeyValuePair<string, object?>("status", "success"));
                    // If we processed messages, check again immediately in case there are more
                    continue;
                }

                consecutiveFailures = 0;
                retryAttempt = 0;
                _processingDuration.Record(stopwatch.Elapsed.TotalSeconds, 
                    new KeyValuePair<string, object?>("status", "no_messages"));

                // No messages to process, wait for the polling interval
                await Task.Delay(_pollingInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                _logger.LogInformation("Outbox processor cancelled");
                break;
            }
            catch (Exception ex)
            {
                consecutiveFailures++;
                retryAttempt++;
                
                _logger.LogError(ex, "Error occurred while processing outbox messages (attempt {RetryAttempt}, consecutive failures: {ConsecutiveFailures})", 
                    retryAttempt, consecutiveFailures);
                
                _messagesFailedCounter.Add(1, 
                    new KeyValuePair<string, object?>("reason", "processing_error"));
                _processingDuration.Record(stopwatch.Elapsed.TotalSeconds, 
                    new KeyValuePair<string, object?>("status", "error"));

                // Calculate exponential backoff with jitter
                var delay = CalculateRetryDelay(consecutiveFailures);
                
                try
                {
                    _logger.LogInformation("Waiting {Delay}ms before retrying due to consecutive failures", delay.TotalMilliseconds);
                    await Task.Delay(delay, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }

                // Reset retry attempt counter after successful delay
                if (consecutiveFailures >= _maxRetryAttempts)
                {
                    _logger.LogWarning("Maximum consecutive failures reached ({MaxFailures}), resetting counter", _maxRetryAttempts);
                    consecutiveFailures = 0;
                }
            }
        }

        _logger.LogInformation("Outbox processor service stopped");
    }

    private async Task<bool> ProcessWithRetry(
        Func<Task<bool>> operation, 
        string operationName, 
        CancellationToken cancellationToken)
    {
        var attempt = 0;
        
        while (attempt < _maxRetryAttempts)
        {
            try
            {
                using var activity = _activitySource.StartActivity($"{operationName}.Attempt{attempt + 1}");
                var result = await operation();
                
                if (result)
                {
                    _messagesProcessedCounter.Add(1);
                }
                
                return result;
            }
            catch (Exception ex) when (attempt < _maxRetryAttempts - 1)
            {
                attempt++;
                var delay = CalculateRetryDelay(attempt);
                
                _logger.LogWarning(ex, "Operation {OperationName} failed on attempt {Attempt}, retrying in {Delay}ms", 
                    operationName, attempt, delay.TotalMilliseconds);
                
                _messagesFailedCounter.Add(1, 
                    new KeyValuePair<string, object?>("reason", "retry"),
                    new KeyValuePair<string, object?>("attempt", attempt.ToString()));
                
                try
                {
                    await Task.Delay(delay, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
            }
        }
        
        // If we get here, all retries failed
        _logger.LogError("Operation {OperationName} failed after {MaxAttempts} attempts", operationName, _maxRetryAttempts);
        throw new InvalidOperationException($"Operation {operationName} failed after {_maxRetryAttempts} attempts");
    }

    private TimeSpan CalculateRetryDelay(int attemptNumber)
    {
        // Exponential backoff with jitter
        var baseDelayMs = (int)_baseRetryDelay.TotalMilliseconds;
        var exponentialDelay = TimeSpan.FromMilliseconds(baseDelayMs * Math.Pow(2, attemptNumber - 1));
        
        // Add jitter (±25% of the delay)
        var jitterRange = exponentialDelay.TotalMilliseconds * 0.25;
        var jitter = (Random.Shared.NextDouble() - 0.5) * 2 * jitterRange;
        var finalDelay = TimeSpan.FromMilliseconds(exponentialDelay.TotalMilliseconds + jitter);
        
        // Ensure we don't exceed the maximum delay
        return finalDelay > _maxRetryDelay ? _maxRetryDelay : finalDelay;
    }

    public override void Dispose()
    {
        _meter?.Dispose();
        _activitySource?.Dispose();
        base.Dispose();
    }
}
