using System.Text;
using AspireApp2.Api.Application.Interfaces;
using Confluent.Kafka;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Services;

/// <summary>
///     Service implementation for Kafka message production with resilience
/// </summary>
public class KafkaProducerService : IKafkaProducerService
{
    private readonly ILogger<KafkaProducerService> _logger;
    private readonly IProducer<string, string> _producer;

    public KafkaProducerService(IProducer<string, string> producer, ILogger<KafkaProducerService> logger)
    {
        _producer = producer;
        _logger = logger;
    }

    public async Task PublishAsync(string topic, string key, string message,
        CancellationToken cancellationToken = default)
    {
        var maxRetries = 3;
        var retryDelay = TimeSpan.FromMilliseconds(500);
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                var kafkaMessage = new Message<string, string>
                {
                    Key = key,
                    Value = message,
                    Headers = new Headers
                    {
                        { "timestamp", Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("O")) },
                        { "source", Encoding.UTF8.GetBytes("AspireApp2.Api") },
                        { "attempt", Encoding.UTF8.GetBytes(attempt.ToString()) }
                    }
                };

                DeliveryResult<string, string>? deliveryResult =
                    await _producer.ProduceAsync(topic, kafkaMessage, cancellationToken);

                _logger.LogDebug(
                    "Successfully published message to topic {Topic} with key {Key}. Partition: {Partition}, Offset: {Offset}, Attempt: {Attempt}",
                    topic, key, deliveryResult.Partition.Value, deliveryResult.Offset.Value, attempt);
                
                return; // Success, exit retry loop
            }
            catch (ProduceException<string, string> ex) when (attempt < maxRetries && IsRetriableError(ex))
            {
                _logger.LogWarning(ex,
                    "Retriable error publishing message to topic {Topic} with key {Key} on attempt {Attempt}/{MaxRetries}. Error: {ErrorCode} - {ErrorReason}",
                    topic, key, attempt, maxRetries, ex.Error.Code, ex.Error.Reason);
                
                if (attempt < maxRetries)
                {
                    await Task.Delay(TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * attempt), cancellationToken);
                }
            }
            catch (ProduceException<string, string> ex)
            {
                _logger.LogError(ex,
                    "Non-retriable error publishing message to topic {Topic} with key {Key}. Error: {ErrorCode} - {ErrorReason}",
                    topic, key, ex.Error.Code, ex.Error.Reason);
                throw;
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning("Publishing operation was cancelled for topic {Topic} with key {Key}", topic, key);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error publishing message to topic {Topic} with key {Key} on attempt {Attempt}/{MaxRetries}", 
                    topic, key, attempt, maxRetries);
                
                if (attempt == maxRetries)
                {
                    throw;
                }
                
                await Task.Delay(TimeSpan.FromMilliseconds(retryDelay.TotalMilliseconds * attempt), cancellationToken);
            }
        }
    }
    
    private static bool IsRetriableError(ProduceException<string, string> ex)
    {
        return ex.Error.Code switch
        {
            ErrorCode.NetworkException => true,
            ErrorCode.RequestTimedOut => true,
            ErrorCode.NotEnoughReplicas => true,
            ErrorCode.NotEnoughReplicasAfterAppend => true,
            ErrorCode.LeaderNotAvailable => true,
            ErrorCode.NotLeaderForPartition => true,
            ErrorCode.BrokerNotAvailable => true,
            _ => false
        };
    }
}
