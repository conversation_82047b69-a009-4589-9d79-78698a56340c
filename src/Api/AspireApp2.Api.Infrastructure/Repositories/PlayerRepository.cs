using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;
using AspireApp2.Api.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AspireApp2.Api.Infrastructure.Repositories;

/// <summary>
///     Repository implementation for Player entity
/// </summary>
public class PlayerRepository : IPlayerRepository
{
    private readonly ApiDbContext _context;
    private readonly ILogger<PlayerRepository> _logger;

    public PlayerRepository(ApiDbContext context, ILogger<PlayerRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<IEnumerable<Player>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Players
                .Where(p => p.IsActive)
                .OrderBy(p => p.Team)
                .ThenBy(p => p.JerseyNumber)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all players");
            throw;
        }
    }

    public async Task<Player?> GetByIdAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Players
                .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving player {PlayerId}", id);
            throw;
        }
    }

    public async Task<Player?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Players
                .FirstOrDefaultAsync(p => p.Email.ToLower() == email.ToLower(), cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving player by email {Email}", email);
            throw;
        }
    }

    public async Task<IEnumerable<Player>> GetByTeamAsync(string team, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Players
                .Where(p => p.Team.ToLower() == team.ToLower() && p.IsActive)
                .OrderBy(p => p.JerseyNumber)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving players for team {Team}", team);
            throw;
        }
    }

    public async Task<Player> CreateAsync(Player player, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Players.Add(player);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Created player {PlayerId} - {PlayerName}", player.Id, player.FullName);
            return player;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating player {PlayerName}", player.FullName);
            throw;
        }
    }

    public async Task<Player> UpdateAsync(Player player, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.Entry(player).State = EntityState.Modified;
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Updated player {PlayerId} - {PlayerName}", player.Id, player.FullName);
            return player;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating player {PlayerId}", player.Id);
            throw;
        }
    }

    public async Task<bool> DeleteAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            Player? player = await GetByIdAsync(id, cancellationToken);
            if (player == null)
            {
                return false;
            }

            // Soft delete
            player.Deactivate();
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Deleted player {PlayerId} - {PlayerName}", player.Id, player.FullName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting player {PlayerId}", id);
            throw;
        }
    }

    public async Task<bool> ExistsAsync(int id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.Players
                .AnyAsync(p => p.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if player {PlayerId} exists", id);
            throw;
        }
    }

    public async Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            IQueryable<Player> query = _context.Players.Where(p => p.Email.ToLower() == email.ToLower());

            if (excludeId.HasValue)
            {
                query = query.Where(p => p.Id != excludeId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email uniqueness for {Email}", email);
            throw;
        }
    }

    public async Task<bool> IsJerseyNumberUniqueAsync(int jerseyNumber, string team, int? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            IQueryable<Player> query = _context.Players
                .Where(p => p.JerseyNumber == jerseyNumber && p.Team.ToLower() == team.ToLower());

            if (excludeId.HasValue)
            {
                query = query.Where(p => p.Id != excludeId.Value);
            }

            return !await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking jersey number uniqueness for {JerseyNumber} in team {Team}",
                jerseyNumber, team);
            throw;
        }
    }
}
