using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Events;

/// <summary>
///     Domain event raised when an item is created
/// </summary>
public class ItemCreatedEvent : IDomainEvent
{
    public ItemCreatedEvent(Item item)
    {
        Item = item;
    }

    public Item Item { get; }
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
