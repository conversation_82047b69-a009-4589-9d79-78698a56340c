using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Events;

/// <summary>
///     Domain event raised when a player is updated
/// </summary>
public class PlayerUpdatedEvent : IDomainEvent
{
    public PlayerUpdatedEvent(Player player)
    {
        Player = player;
    }

    public Player Player { get; }
    public Guid Id { get; } = Guid.NewGuid();
    public DateTime OccurredOn { get; } = DateTime.UtcNow;
}
