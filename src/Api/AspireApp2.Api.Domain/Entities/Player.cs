namespace AspireApp2.Api.Domain.Entities;

/// <summary>
///     Represents a player entity in the domain
/// </summary>
public class Player
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
    public int JerseyNumber { get; set; }
    public DateTime DateOfBirth { get; set; }
    public string Team { get; set; } = string.Empty;
    public decimal Salary { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }

    // Computed properties
    public string FullName => $"{FirstName} {LastName}";

    public int Age => DateTime.UtcNow.Year - DateOfBirth.Year -
                      (DateTime.UtcNow.DayOfYear < DateOfBirth.DayOfYear ? 1 : 0);

    // Domain methods
    public void UpdateDetails(string firstName, string lastName, string email, string position,
        int jerseyNumber, DateTime dateOfBirth, string team, decimal salary)
    {
        FirstName = firstName?.Trim() ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName?.Trim() ?? throw new ArgumentNullException(nameof(lastName));
        Email = email?.Trim().ToLowerInvariant() ?? throw new ArgumentNullException(nameof(email));
        Position = position?.Trim() ?? throw new ArgumentNullException(nameof(position));
        JerseyNumber = jerseyNumber;
        DateOfBirth = dateOfBirth;
        Team = team?.Trim() ?? throw new ArgumentNullException(nameof(team));
        Salary = salary;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    // Domain validation
    public bool IsValidAge()
    {
        return Age >= 16 && Age <= 50; // Reasonable age range for players
    }

    public bool IsValidSalary()
    {
        return Salary >= 0;
    }
}
