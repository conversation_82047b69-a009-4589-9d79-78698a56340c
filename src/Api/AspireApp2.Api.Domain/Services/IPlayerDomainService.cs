using AspireApp2.Api.Domain.Entities;

namespace AspireApp2.Api.Domain.Services;

/// <summary>
///     Domain service for Player business logic
/// </summary>
public interface IPlayerDomainService
{
    Task<bool> ValidatePlayerCreationAsync(Player player, CancellationToken cancellationToken = default);
    Task<bool> ValidatePlayerUpdateAsync(Player player, CancellationToken cancellationToken = default);

    Task<bool> CanAssignJerseyNumberAsync(int jerseyNumber, string team, int? excludePlayerId = null,
        CancellationToken cancellationToken = default);

    Task<bool> IsEmailAvailableAsync(string email, int? excludePlayerId = null,
        CancellationToken cancellationToken = default);
}
