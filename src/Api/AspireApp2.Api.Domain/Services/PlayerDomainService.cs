using AspireApp2.Api.Domain.Entities;
using AspireApp2.Api.Domain.Repositories;

namespace AspireApp2.Api.Domain.Services;

/// <summary>
///     Domain service implementation for Player business logic
/// </summary>
public class PlayerDomainService : IPlayerDomainService
{
    private readonly IPlayerRepository _playerRepository;

    public PlayerDomainService(IPlayerRepository playerRepository)
    {
        _playerRepository = playerRepository;
    }

    public async Task<bool> ValidatePlayerCreationAsync(Player player, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        if (!player.IsValidAge())
        {
            return false;
        }

        if (!player.IsValidSalary())
        {
            return false;
        }

        // Check email uniqueness
        if (!await IsEmailAvailableAsync(player.Email, null, cancellationToken))
        {
            return false;
        }

        // Check jersey number uniqueness within team
        if (!await CanAssignJerseyNumberAsync(player.JerseyNumber, player.Team, null, cancellationToken))
        {
            return false;
        }

        return true;
    }

    public async Task<bool> ValidatePlayerUpdateAsync(Player player, CancellationToken cancellationToken = default)
    {
        // Validate business rules
        if (!player.IsValidAge())
        {
            return false;
        }

        if (!player.IsValidSalary())
        {
            return false;
        }

        // Check email uniqueness (excluding current player)
        if (!await IsEmailAvailableAsync(player.Email, player.Id, cancellationToken))
        {
            return false;
        }

        // Check jersey number uniqueness within team (excluding current player)
        if (!await CanAssignJerseyNumberAsync(player.JerseyNumber, player.Team, player.Id, cancellationToken))
        {
            return false;
        }

        return true;
    }

    public async Task<bool> CanAssignJerseyNumberAsync(int jerseyNumber, string team, int? excludePlayerId = null,
        CancellationToken cancellationToken = default)
    {
        return await _playerRepository.IsJerseyNumberUniqueAsync(jerseyNumber, team, excludePlayerId,
            cancellationToken);
    }

    public async Task<bool> IsEmailAvailableAsync(string email, int? excludePlayerId = null,
        CancellationToken cancellationToken = default)
    {
        return await _playerRepository.IsEmailUniqueAsync(email, excludePlayerId, cancellationToken);
    }
}
