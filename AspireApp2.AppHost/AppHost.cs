using Projects;

IDistributedApplicationBuilder builder = DistributedApplication.CreateBuilder(args);

// Add infrastructure services with proper health checks
IResourceBuilder<RedisResource> cache = builder.AddRedis("cache")
    .WithDataVolume();

IResourceBuilder<SqlServerDatabaseResource> sqlServer = builder.AddSqlServer("sql")
    .WithDataVolume()
    .AddDatabase("DefaultConnection");

IResourceBuilder<MongoDBDatabaseResource> mongodb = builder.AddMongoDB("mongodb")
    .WithDataVolume()
    .AddDatabase("PlayerEvents");

// Add Kafka with enhanced configuration
IResourceBuilder<KafkaServerResource> kafka = builder.AddKafka("kafka")
    .WithKafkaUI(); // Kafka UI for monitoring

// API Service - wait for all dependencies to be ready
IResourceBuilder<ProjectResource> apiService = builder.AddProject<Projects.AspireApp2_Api_Presentation>("apiservice")
    .WithReference(sqlServer)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(sqlServer)
    .WaitFor(kafka);

// Player Event Service - wait for dependencies
IResourceBuilder<ProjectResource> playerEventService = builder
    .AddProject<Projects.AspireApp2_PlayerEventService>("playereventservice")
    .WithReference(mongodb)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(mongodb)
    .WaitFor(kafka);

// Web Frontend - wait for cache and API to be ready
builder.AddProject<Projects.AspireApp2_Web>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WithReference(apiService)
    .WaitFor(cache)
    .WaitFor(apiService);

builder.Build().Run();
